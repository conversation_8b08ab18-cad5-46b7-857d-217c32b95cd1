"use client"
import { Search } from 'lucide-react';

interface SearchInputProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  placeholder?: string;
}

const SearchInput = ({ searchQuery, onSearchChange, placeholder = "Places to go, things to do, hotels..." }: SearchInputProps) => {
  return (
    <div className="relative max-w-2xl mx-auto">
      <div className="relative bg-gray-100 rounded-2xl border border-gray-200">
        <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          placeholder={placeholder}
          className="w-full pl-14 pr-28 py-4 text-base bg-transparent border-0 rounded-2xl focus:outline-none text-gray-700 placeholder-gray-500"
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-2">
          <button className="bg-green-400 hover:bg-green-500 text-black font-medium py-3 px-6 rounded-xl transition-colors duration-200">
            Search
          </button>
        </div>
      </div>
    </div>
  );
};

// Demo component to show the search input
const SearchDemo = () => {
  const [searchQuery, setSearchQuery] = React.useState('');
  
  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-8">
      <SearchInput 
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />
    </div>
  );
};

export default SearchDemo;